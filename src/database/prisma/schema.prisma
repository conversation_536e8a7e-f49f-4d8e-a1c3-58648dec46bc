generator serverClient {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                         String                      @id
  marketplaceId              String
  name                       String?                     @db.VarChar
  balance                    Decimal                     @default(0) @db.Decimal(10, 2)
  debt                       Decimal                     @default(0) @db.Decimal(10, 2)
  currency                   String                      @db.VarChar
  marketplace                Marketplace                 @relation(fields: [marketplaceId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  gatewayAccounts            GatewayAccount[]
  paymentIntentDistributions PaymentIntentDistribution[]
  paymentSplits              PaymentSplit[]
  paymentSplitsRefunds       PaymentSplitRefund[]
  transactions               Transaction[]
  createdAt                  DateTime                    @default(now())
  updatedAt                  DateTime                    @default(now()) @updatedAt
}

model Customer {
  id               String            @id
  marketplaceId    String
  name             String?           @db.VarChar
  email            String?           @db.VarChar
  idempotencyKey   String?           @unique @db.VarChar
  marketplace      Marketplace       @relation(fields: [marketplaceId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  gatewayCustomers GatewayCustomer[]
  payments         Payment[]
  paymentIntents   PaymentIntent[]
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @default(now()) @updatedAt
}

model Gateway {
  id            String  @id
  name          String  @db.VarChar
  type          String  @unique
  feeFixed      Decimal @db.Decimal(10, 2)
  feePercentage Decimal @db.Decimal(5, 4)

  gatewayAccounts       GatewayAccount[]
  gatewayCustomers      GatewayCustomer[]
  payments              Payment[]
  paymentIntents        PaymentIntent[]
  disputes              Dispute[]
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @default(now()) @updatedAt
  webhooks              Webhook[]
  paymentSplits         PaymentSplit[]
  marketplaceGateways   MarketplaceGateway[]
  payouts               Payout[]
  gatewayPaymentMethods GatewayPaymentMethod[]
}

model GatewayAccount {
  id          String   @id
  idAtGateway String
  accountId   String
  gatewayId   String
  account     Account  @relation(fields: [accountId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  gateway     Gateway  @relation(fields: [gatewayId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now()) @updatedAt

  @@unique([accountId, gatewayId, idAtGateway], map: "GatewayAccount_accountId_gatewayId_idAtGateway_idx")
}

model GatewayCustomer {
  id          String   @id
  idAtGateway String   @unique
  customerId  String
  gatewayId   String
  customer    Customer @relation(fields: [customerId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  gateway     Gateway  @relation(fields: [gatewayId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now()) @updatedAt

  @@unique([customerId, gatewayId], map: "GatewayCustomer_customerId_gatewayId_idx")
}

model Marketplace {
  id                  String               @id
  name                String?              @db.VarChar
  description         String?
  webhookUrl          String
  webhookSecretId     String
  secretKeyId         String
  publishableKey      String
  accounts            Account[]
  customers           Customer[]
  paymentIntents      PaymentIntent[]
  payments            Payment[]
  webhookSecret       ManagedSecret        @relation(fields: [webhookSecretId], references: [id], "Marketplace_secretKeyIdToManagedSecret")
  secretKey           ManagedSecret        @relation(fields: [secretKeyId], references: [id], "Marketplace_webhookSecretIdToManagedSecret")
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @default(now()) @updatedAt
  webhooks            Webhook[]
  marketplaceGateways MarketplaceGateway[]
  payouts             Payout[]
}

model MarketplaceGateway {
  id                              String  @id
  balance                         Decimal @default(0) @db.Decimal(10, 2)
  currency                        String  @db.VarChar
  marketplaceId                   String
  gatewayId                       String
  marketplacePaymentFeeFixed      Decimal @db.Decimal(10, 2)
  marketplacePaymentFeePercentage Decimal @db.Decimal(5, 4)
  platformPaymentFeeFixed         Decimal @db.Decimal(10, 2)
  platformPaymentFeePercentage    Decimal @db.Decimal(5, 4)

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  marketplace                Marketplace                     @relation(fields: [marketplaceId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  gateway                    Gateway                         @relation(fields: [gatewayId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  transactions               MarketplaceGatewayTransaction[]
  marketplaceGatewayAccounts MarketplaceGatewayAccount[]
  feeSettings                FeeSettings[]
}

model MarketplaceGatewayTransaction {
  id                   String             @id
  marketplaceGatewayId String
  amount               Decimal            @db.Decimal(10, 2)
  currency             String             @db.VarChar
  transactionId        String?
  marketplaceGateway   MarketplaceGateway @relation(fields: [marketplaceGatewayId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  transaction          Transaction?       @relation(fields: [transactionId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  createdAt            DateTime           @default(now())
  updatedAt            DateTime           @default(now()) @updatedAt
}

model Payment {
  id                              String  @id
  marketplaceId                   String
  customerId                      String
  gatewayId                       String
  paymentMethodId                 String?
  amount                          Decimal @db.Decimal(10, 2)
  refundedAmount                  Decimal @default(0) @db.Decimal(10, 2)
  paymentIntentId                 String  @unique
  currency                        String  @db.VarChar
  status                          String  @db.VarChar
  paymentMethodType               String  @db.VarChar
  version                         Int     @default(1)
  marketplacePaymentFeeFixed      Decimal @db.Decimal(10, 2)
  marketplacePaymentFeePercentage Decimal @db.Decimal(5, 4)
  marketplacePaymentFee           Decimal @db.Decimal(10, 2)
  platformPaymentFeeFixed         Decimal @db.Decimal(10, 2)
  platformPaymentFeePercentage    Decimal @db.Decimal(5, 4)
  platformPaymentFee              Decimal @db.Decimal(10, 2)
  providerFeeFixed                Decimal @db.Decimal(10, 2)
  providerFeePercentage           Decimal @db.Decimal(5, 4)
  providerFee                     Decimal @db.Decimal(10, 2)
  marketplaceOrderFee             Decimal @default(0) @db.Decimal(10, 2)
  platformOrderFee                Decimal @default(0) @db.Decimal(10, 2)
  platformPaymentFeeRefunded      Decimal @default(0) @db.Decimal(10, 2)
  providerPaymentFeeRefunded      Decimal @default(0) @db.Decimal(10, 2)
  marketplacePaymentFeeRefunded   Decimal @default(0) @db.Decimal(10, 2)
  marketplaceOrderFeeRefunded     Decimal @default(0) @db.Decimal(10, 2)
  platformOrderFeeRefunded        Decimal @default(0) @db.Decimal(10, 2)
  fingerprint                     String?
  feeSettingsId                   String?

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  paymentMethod  PaymentMethod?  @relation(fields: [paymentMethodId], references: [id])
  customer       Customer        @relation(fields: [customerId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  gateway        Gateway         @relation(fields: [gatewayId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  paymentIntent  PaymentIntent   @relation(fields: [paymentIntentId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  paymentSplits  PaymentSplit[]
  refunds        Refund[]
  transactions   Transaction[]
  gatewayRefunds GatewayRefund[]
  marketplace    Marketplace     @relation(fields: [marketplaceId], references: [id])
  payouts        Payout[]
  feeSettings    FeeSettings?    @relation(fields: [feeSettingsId], references: [id], onDelete: SetNull, onUpdate: Cascade)
}

model PaymentIntent {
  id                              String  @id
  marketplaceId                   String
  amount                          Decimal @db.Decimal(10, 2)
  currency                        String  @db.VarChar
  status                          String  @db.VarChar
  paymentMethodType               String  @db.VarChar
  statementDescriptor             String? @db.VarChar
  customerId                      String
  gatewayId                       String
  businessEntityId                String? @db.VarChar
  metadata                        Json    @default("{}")
  marketplacePaymentFeeFixed      Decimal @db.Decimal(10, 2)
  marketplacePaymentFeePercentage Decimal @db.Decimal(5, 4)
  marketplacePaymentFee           Decimal @db.Decimal(10, 2)
  platformPaymentFeeFixed         Decimal @db.Decimal(10, 2)
  platformPaymentFeePercentage    Decimal @db.Decimal(5, 4)
  platformPaymentFee              Decimal @db.Decimal(10, 2)
  providerFeeFixed                Decimal @db.Decimal(10, 2)
  providerFeePercentage           Decimal @db.Decimal(5, 4)
  providerFee                     Decimal @db.Decimal(10, 2)
  marketplaceOrderFee             Decimal @default(0) @db.Decimal(10, 2)
  platformOrderFee                Decimal @default(0) @db.Decimal(10, 2)
  fingerprint                     String?
  feeSettingsId                   String?

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  payments                   Payment[]
  customer                   Customer                    @relation(fields: [customerId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  gateway                    Gateway                     @relation(fields: [gatewayId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  marketplace                Marketplace                 @relation(fields: [marketplaceId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  paymentIntentDistributions PaymentIntentDistribution[]
  stripePaymentIntents       StripePaymentIntent[]
  disputes                   Dispute[]
  feeSettings                FeeSettings?                @relation(fields: [feeSettingsId], references: [id], onDelete: SetNull, onUpdate: Cascade)
}

model PaymentIntentDistribution {
  paymentIntentId String
  accountId       String
  amount          Decimal       @db.Decimal(10, 2)
  currency        String        @db.VarChar
  account         Account       @relation(fields: [accountId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  paymentIntent   PaymentIntent @relation(fields: [paymentIntentId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @default(now()) @updatedAt

  @@id([paymentIntentId, accountId])
}

model PaymentMethod {
  id              String   @id
  type            String   @db.VarChar
  cardLast4       String?  @db.VarChar
  cardExpiryYear  Int?
  cardExpiryMonth Int?
  cardType        String?  @db.VarChar
  createdAt       DateTime @default(now())
  updatedAt       DateTime @default(now()) @updatedAt

  gatewayPaymentMethod GatewayPaymentMethod?
  payments             Payment[]
}

model GatewayPaymentMethod {
  id              String        @id
  idAtGateway     String
  paymentMethod   PaymentMethod @relation(fields: [paymentMethodId], references: [id])
  paymentMethodId String        @unique
  fingerprint     String?
  gateway         Gateway       @relation(fields: [gatewayId], references: [id], onDelete: Cascade)
  gatewayId       String
}

model PaymentSplit {
  id                  String               @id
  accountId           String
  paymentId           String
  gatewayId           String?
  amount              Decimal              @db.Decimal(10, 2)
  refundedAmount      Decimal              @default(0) @db.Decimal(10, 2)
  status              String               @db.VarChar
  currency            String               @db.VarChar
  metadata            Json                 @default("{}")
  account             Account              @relation(fields: [accountId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  payment             Payment              @relation(fields: [paymentId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  gateway             Gateway?             @relation(fields: [gatewayId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  paymentSplitRefunds PaymentSplitRefund[]
  transactions        Transaction[]
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @default(now()) @updatedAt
}

model PaymentSplitRefund {
  id             String        @id
  accountId      String
  refundId       String?
  disputeId      String?
  isManual       Boolean       @default(false)
  paymentSplitId String
  status         String        @db.VarChar
  amount         Decimal       @db.Decimal(10, 2)
  currency       String        @db.VarChar
  metadata       Json          @default("{}")
  account        Account       @relation(fields: [accountId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  paymentSplit   PaymentSplit  @relation(fields: [paymentSplitId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  refund         Refund?       @relation(fields: [refundId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  dispute        Dispute?      @relation(fields: [disputeId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  transactions   Transaction[]
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @default(now()) @updatedAt
}

model Refund {
  id                            String  @id
  paymentId                     String
  amount                        Decimal @db.Decimal(10, 2)
  currency                      String  @db.VarChar
  status                        String  @db.VarChar
  type                          String  @db.VarChar
  metadata                      Json    @default("{}")
  isManual                      Boolean @default(false)
  platformPaymentFeeRefunded    Decimal @db.Decimal(10, 2)
  providerPaymentFeeRefunded    Decimal @db.Decimal(10, 2)
  marketplacePaymentFeeRefunded Decimal @db.Decimal(10, 2)
  marketplaceOrderFeeRefunded   Decimal @db.Decimal(10, 2)
  platformOrderFeeRefunded      Decimal @db.Decimal(10, 2)

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  paymentSplitRefunds PaymentSplitRefund[]
  payment             Payment              @relation(fields: [paymentId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  stripeRefunds       StripeRefund[]
  transactions        Transaction[]
  gatewayRefund       GatewayRefund?
  payoutReversals     PayoutReversal[]
}

model StripePaymentIntent {
  id                    String        @id
  paymentIntentId       String
  stripePaymentIntentId String        @unique @db.VarChar
  stripeCustomerId      String        @db.VarChar
  clientSecret          String        @db.VarChar
  paymentIntent         PaymentIntent @relation(fields: [paymentIntentId], references: [id], onDelete: Cascade, onUpdate: Cascade)
}

model StripeRefund {
  id             String  @id
  refundId       String
  stripeRefundId String  @unique @db.VarChar
  amount         Decimal @db.Decimal(10, 2)
  currency       String  @db.VarChar
  stripeStatus   String  @db.VarChar
  refund         Refund  @relation(fields: [refundId], references: [id], onDelete: Cascade, onUpdate: Cascade)
}

model Transaction {
  id                            String                          @id
  amount                        Decimal                         @db.Decimal(10, 2)
  currency                      String                          @db.VarChar
  type                          String                          @db.VarChar
  status                        String                          @db.VarChar
  paymentId                     String?
  accountId                     String?
  refundId                      String?
  paymentSplitId                String?
  paymentSplitRefundId          String?
  disputeId                     String?
  dispute                       Dispute?                        @relation(fields: [disputeId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  account                       Account?                        @relation(fields: [accountId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  payment                       Payment?                        @relation(fields: [paymentId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  paymentSplit                  PaymentSplit?                   @relation(fields: [paymentSplitId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  paymentSplitRefund            PaymentSplitRefund?             @relation(fields: [paymentSplitRefundId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  refund                        Refund?                         @relation(fields: [refundId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  marketplaceGatewayTransaction MarketplaceGatewayTransaction[]
  createdAt                     DateTime                        @default(now())
  updatedAt                     DateTime                        @default(now()) @updatedAt
}

model StripeEvent {
  id            String  @id
  eventObjectId String?
  status        String?
  type          String
  data          Json

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model ManagedSecret {
  id                          String        @id
  hash                        String        @unique
  idAtProvider                String        @unique
  providerType                String
  createdAt                   DateTime      @default(now())
  updatedAt                   DateTime      @default(now()) @updatedAt
  marketplacesBySecretKey     Marketplace[] @relation("Marketplace_secretKeyIdToManagedSecret")
  marketplaceByWebhookSecrets Marketplace[] @relation("Marketplace_webhookSecretIdToManagedSecret")
}

model Webhook {
  id            String      @id
  marketplaceId String
  marketplace   Marketplace @relation(fields: [marketplaceId], references: [id])
  type          String
  gateway       Gateway     @relation(fields: [gatewayId], references: [id])
  object        String
  status        String
  failReason    String?
  fails         Int         @db.SmallInt
  lastAttemptAt DateTime?
  nextRetryAt   DateTime?
  data          Json
  url           String
  gatewayId     String
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @default(now()) @updatedAt
}

model GatewayTransfer {
  id                   String   @id
  idAtGateway          String   @unique
  source               String
  sourceId             String
  version              Int      @default(1)
  idempotencyKey       String
  amount               Decimal  @db.Decimal(10, 2)
  currency             String   @db.VarChar
  status               String
  destinationAccountId String
  gatewayType          String
  createdAt            DateTime @default(now())
  updatedAt            DateTime @default(now()) @updatedAt
}

model GatewayRefund {
  id             String   @id
  idAtGateway    String   @unique
  idempotencyKey String
  amount         Decimal  @db.Decimal(10, 2)
  currency       String   @db.VarChar
  payment        Payment  @relation(fields: [paymentId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  paymentId      String
  refund         Refund   @relation(fields: [refundId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  refundId       String   @unique
  gatewayType    String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @default(now()) @updatedAt
}

model Debt {
  id         String  @id
  accountId  String
  amount     Decimal @db.Decimal(10, 2)
  currency   String  @db.VarChar
  paidAmount Decimal @db.Decimal(10, 2)
  type       String
  status     String
  source     String
  sourceId   String
  version    Int     @default(1)

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt
}

model StripeDispute {
  id                    String  @id
  disputeId             String  @unique
  stripeDisputeId       String  @unique @db.VarChar
  stripePaymentIntentId String  @db.VarChar
  dispute               Dispute @relation(fields: [disputeId], references: [id], onDelete: Cascade, onUpdate: Cascade)
}

model Dispute {
  id                  String               @id
  paymentIntentId     String
  paymentId           String
  amount              Decimal              @db.Decimal(10, 2)
  currency            String               @db.VarChar
  status              String               @db.VarChar
  reason              String               @db.VarChar
  gatewayId           String
  gateway             Gateway              @relation(fields: [gatewayId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  evidence            Json
  evidenceDetails     Json
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @default(now()) @updatedAt
  paymentIntent       PaymentIntent        @relation(fields: [paymentIntentId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  stripeDispute       StripeDispute[]
  transactions        Transaction[]
  paymentSplitRefunds PaymentSplitRefund[]
}

model MarketplaceGatewayAccount {
  id                   String             @id
  name                 String?            @db.VarChar
  idAtGateway          String
  marketplaceGatewayId String
  marketplaceGateway   MarketplaceGateway @relation(fields: [marketplaceGatewayId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  createdAt            DateTime           @default(now())
  updatedAt            DateTime           @default(now()) @updatedAt

  payouts Payout[]
}

model Payout {
  id                          String                    @id
  amount                      Decimal                   @db.Decimal(10, 2)
  reversedAmount              Decimal                   @db.Decimal(10, 2)
  currency                    String                    @db.VarChar
  marketplaceGatewayAccountId String
  marketplaceGatewayAccount   MarketplaceGatewayAccount @relation(fields: [marketplaceGatewayAccountId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  gatewayId                   String
  gateway                     Gateway                   @relation(fields: [gatewayId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  marketplaceId               String
  marketplace                 Marketplace               @relation(fields: [marketplaceId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  paymentId                   String?
  payment                     Payment?                  @relation(fields: [paymentId], references: [id], onDelete: Cascade, onUpdate: Cascade)

  status String
  type   String

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  payoutReversals PayoutReversal[]
}

model PayoutReversal {
  id       String  @id
  amount   Decimal @db.Decimal(10, 2)
  currency String  @db.VarChar
  payoutId String
  refundId String?
  status   String

  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  refund Refund? @relation(fields: [refundId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  payout Payout  @relation(fields: [payoutId], references: [id], onDelete: Cascade, onUpdate: Cascade)
}

model FeeSettings {
  id                       String   @id
  marketplaceFeeFixed      Decimal  @db.Decimal(10, 2)
  marketplaceFeePercentage Decimal  @db.Decimal(5, 4)
  platformFeeFixed         Decimal  @db.Decimal(10, 2)
  platformFeePercentage    Decimal  @db.Decimal(5, 4)
  isDefault                Boolean
  createdAt                DateTime @default(now())
  updatedAt                DateTime @default(now()) @updatedAt
  marketplaceGatewayId     String

  marketplaceGateway MarketplaceGateway @relation(fields: [marketplaceGatewayId], references: [id], onDelete: Cascade, onUpdate: Cascade)
  payments           Payment[]
  paymentIntents     PaymentIntent[]
}
