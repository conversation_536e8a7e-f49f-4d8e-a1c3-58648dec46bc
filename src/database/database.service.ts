import { Injectable } from '@nestjs/common';

import { ExtendedPrismaClient } from './prisma/prisma-client';
import { DatabaseConfig } from '../config/database.config';

@Injectable()
export class DatabaseService extends ExtendedPrismaClient {
  constructor() {
    super({
      datasources: {
        db: { url: DatabaseConfig.databaseUrl },
      },
      transactionOptions: {
        timeout: 10000,
      },
    });
  }
}
