import { Test, TestingModule } from '@nestjs/testing';

import { PaymentIntentApplicationService } from '@/application/payment-intent/services';
import { DatabaseService } from '@/database';
import {
  FEE_SETTINGS_REPOSITORY_TOKEN,
  GATEWAY_REPOSITORY_TOKEN,
  MARKETPLACE_GATEWAY_REPOSITORY_TOKEN,
  PAYMENT_INTENT_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { FeeSettings } from '@/domain/fee-settings/fee-settings';
import { Gateway } from '@/domain/gateway/gateway';
import { MarketplaceGateway } from '@/domain/marketplace-gateway/marketplace-gateway';
import { PaymentIntent } from '@/domain/payment-intent/payment-intent';
import { PaymentSplit } from '@/domain/payment-intent/payment-split.value-object';
import { PaymentIntentDomainService } from '@/domain/payment-intent/services';
import { AllowedCurrencies, GatewayType, Money, PaymentMethodType } from '@/domain/shared';
import { FeeSettingsRepositoryMock } from '@/test/mocks/repositories/fee-settings-repository.mock';
import { GatewayRepositoryMock } from '@/test/mocks/repositories/gateway-repository.mock';
import { MarketplaceGatewayRepositoryMock } from '@/test/mocks/repositories/marketplace-gateway-repository.mock';
import { PaymentIntentRepositoryMock } from '@/test/mocks/repositories/payment-intent-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';
import { PaymentIntentApplicationServiceMock } from '@/test/mocks/services/payment-intent-application-service.mock';
import { PaymentIntentDomainServiceMock } from '@/test/mocks/services/payment-intent-domain-service.mock';

import { CreatePaymentIntentCommand, CreatePaymentIntentCommandHandler } from '.';

describe('CreatePaymentIntentCommandHandler', () => {
  let handler: CreatePaymentIntentCommandHandler;
  let db: DatabaseServiceMock;
  let gatewayRepository: GatewayRepositoryMock;
  let marketplaceGatewayRepository: MarketplaceGatewayRepositoryMock;
  let paymentIntentRepository: PaymentIntentRepositoryMock;
  let paymentIntentDomainService: PaymentIntentDomainService;
  let paymentIntentAppService: PaymentIntentApplicationService;
  let feeSettingsRepository: FeeSettingsRepositoryMock;
  let commandProps: CreatePaymentIntentCommand['props'];
  let gateway: Gateway;
  let marketplaceGateway: MarketplaceGateway;
  let paymentIntent: PaymentIntent;
  let feeSettings: FeeSettings;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreatePaymentIntentCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: PaymentIntentDomainService, useClass: PaymentIntentDomainServiceMock },
        { provide: PaymentIntentApplicationService, useClass: PaymentIntentApplicationServiceMock },
        { provide: GATEWAY_REPOSITORY_TOKEN, useClass: GatewayRepositoryMock },
        { provide: PAYMENT_INTENT_REPOSITORY_TOKEN, useClass: PaymentIntentRepositoryMock },
        {
          provide: MARKETPLACE_GATEWAY_REPOSITORY_TOKEN,
          useClass: MarketplaceGatewayRepositoryMock,
        },
        { provide: FEE_SETTINGS_REPOSITORY_TOKEN, useClass: FeeSettingsRepositoryMock },
      ],
    }).compile();

    handler = module.get<CreatePaymentIntentCommandHandler>(CreatePaymentIntentCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    gatewayRepository = module.get<GatewayRepositoryMock>(GATEWAY_REPOSITORY_TOKEN);
    marketplaceGatewayRepository = module.get<MarketplaceGatewayRepositoryMock>(
      MARKETPLACE_GATEWAY_REPOSITORY_TOKEN,
    );
    paymentIntentRepository = module.get<PaymentIntentRepositoryMock>(
      PAYMENT_INTENT_REPOSITORY_TOKEN,
    );
    paymentIntentDomainService = module.get<PaymentIntentDomainService>(PaymentIntentDomainService);
    paymentIntentAppService = module.get<PaymentIntentApplicationService>(
      PaymentIntentApplicationService,
    );
    feeSettingsRepository = module.get<FeeSettingsRepositoryMock>(FEE_SETTINGS_REPOSITORY_TOKEN);
    [gateway] = gatewayRepository.db;
    [marketplaceGateway] = marketplaceGatewayRepository.db;
    [paymentIntent] = paymentIntentRepository.db;
    [feeSettings] = feeSettingsRepository.db;
    commandProps = {
      amount: 10,
      businessEntityId: 'business-entity-id',
      currency: AllowedCurrencies.USD,
      customerId: 'customer-id',
      gatewayType: gateway.getProps().type,
      marketplaceId: marketplaceGateway.getProps().marketplaceId,
      marketplaceOrderFee: 1,
      metadata: undefined,
      paymentMethodType: PaymentMethodType.CARD,
      paymentSplitsData: [
        { accountId: 'account-id-0', amount: 5 },
        { accountId: 'account-id-1', amount: 5 },
      ],
      platformOrderFee: 0,
      statementDescriptor: '',
    };
    paymentIntentAppService.validateAccountsExist = jest.fn();
    paymentIntentDomainService.create = jest.fn().mockResolvedValue(paymentIntent);
    paymentIntentRepository.create = jest.fn();

    db.catchTransactions = false;
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if gateway is not found', async () => {
    const command = new CreatePaymentIntentCommand({
      ...commandProps,
      gatewayType: 'invalid-gateway' as GatewayType,
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Invalid gateway type'));
  });

  it('should fail if marketplace gateway is not found', async () => {
    const command = new CreatePaymentIntentCommand({
      ...commandProps,
      marketplaceId: 'invalid-marketplace-id',
    });

    await expect(handler.execute(command)).rejects.toThrow(
      new Error('Marketplace gateway is not found'),
    );
  });

  it('should validate accounts exist', async () => {
    const command = new CreatePaymentIntentCommand(commandProps);

    await handler.execute(command);

    expect(paymentIntentAppService.validateAccountsExist).toHaveBeenCalledWith(
      commandProps.marketplaceId,
      commandProps.gatewayType,
      commandProps.paymentSplitsData,
    );
  });

  it('should create payment intent', async () => {
    const command = new CreatePaymentIntentCommand(commandProps);

    await handler.execute(command);

    expect(paymentIntentDomainService.create).toHaveBeenCalledWith({
      marketplaceId: commandProps.marketplaceId,
      gateway,
      customerId: commandProps.customerId,
      feeSettingsId: feeSettings.getId(),
      amount: Money.from(commandProps.amount, commandProps.currency),
      paymentMethodType: commandProps.paymentMethodType,
      paymentSplits: commandProps.paymentSplitsData.map(
        (splitData) =>
          new PaymentSplit({
            accountId: splitData.accountId,
            amount: Money.from(splitData.amount, commandProps.currency),
          }),
      ),
      businessEntityId: commandProps.businessEntityId,
      statementDescriptor: commandProps.statementDescriptor,
      metadata: commandProps.metadata,
      platformPaymentFeeFixed: feeSettings.getProps().platformFeeFixed,
      platformPaymentFeePercentage: feeSettings.getProps().platformFeePercentage,
      marketplacePaymentFeeFixed: feeSettings.getProps().marketplaceFeeFixed,
      marketplacePaymentFeePercentage: feeSettings.getProps().marketplaceFeePercentage,
      providerFeeFixed: gateway.getProps().feeFixed,
      providerFeePercentage: gateway.getProps().feePercentage,
      marketplaceOrderFee: Money.from(commandProps.marketplaceOrderFee, commandProps.currency),
      platformOrderFee: Money.from(commandProps.platformOrderFee, commandProps.currency),
    });
    expect(paymentIntentRepository.create).toHaveBeenCalledWith(paymentIntent);
  });

  it('should operate within a transaction', async () => {
    const command = new CreatePaymentIntentCommand(commandProps);
    db.catchTransactions = true;

    await handler.execute(command);

    expect(paymentIntentDomainService.create).not.toHaveBeenCalled();
    expect(paymentIntentRepository.create).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    const result = await db.invokeNextTransaction();

    expect(paymentIntentDomainService.create).toHaveBeenCalled();
    expect(paymentIntentRepository.create).toHaveBeenCalled();
    expect(result).toBe(paymentIntent);
  });
});
