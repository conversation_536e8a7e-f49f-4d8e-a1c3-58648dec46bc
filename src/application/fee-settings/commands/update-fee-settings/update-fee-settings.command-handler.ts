import { Inject } from '@nestjs/common';
import { Command<PERSON><PERSON><PERSON>, ICommandHandler } from '@nestjs/cqrs';

import { DatabaseService } from '@/database';
import {
  FEE_SETTINGS_REPOSITORY_TOKEN,
  MARKETPLACE_GATEWAY_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { FeeSettings } from '@/domain/fee-settings/fee-settings';
import { FeeSettingsRepositoryPort } from '@/domain/fee-settings/ports';
import { FeeSettingsDomainService } from '@/domain/fee-settings/services/fee-settings.domain-service';
import { MarketplaceGatewayRepositoryPort } from '@/domain/marketplace-gateway/ports';
import { Money } from '@/domain/shared';

import { UpdateFeeSettingsCommand } from './update-fee-settings.command';

@CommandHandler(UpdateFeeSettingsCommand)
export class UpdateFeeSettingsCommandHandler implements ICommandHandler {
  constructor(
    private readonly db: DatabaseService,
    @Inject(FEE_SETTINGS_REPOSITORY_TOKEN)
    private readonly feeSettingsRepository: FeeSettingsRepositoryPort,
    @Inject(MARKETPLACE_GATEWAY_REPOSITORY_TOKEN)
    private readonly marketplaceGatewayRepository: MarketplaceGatewayRepositoryPort,
    private readonly feeSettingsDomainService: FeeSettingsDomainService,
  ) {}

  async execute(command: UpdateFeeSettingsCommand): Promise<FeeSettings> {
    const props = command.getProps();

    const feeSettings = await this.feeSettingsRepository.findById(props.feeSettingsId);

    if (!feeSettings) {
      throw new Error('Entity not found'); // todo: custom error
    }

    const marketplaceGateway = await this.marketplaceGatewayRepository.findById(
      feeSettings.getProps().marketplaceGatewayId,
    );

    if (!marketplaceGateway) {
      throw new Error('Marketplace gateway not found'); // todo: custom error
    }

    const existingFeeSettings = await this.feeSettingsRepository.findByMarketplaceGatewayId(
      feeSettings.getProps().marketplaceGatewayId,
    );

    this.feeSettingsDomainService.updateFeeSettings({
      marketplaceGatewayFeeSettings: existingFeeSettings,
      marketplaceFeeFixed: Money.from(
        props.marketplaceFeeFixed,
        marketplaceGateway.getProps().currency,
      ),
      marketplaceFeePercentage: props.marketplaceFeePercentage,
      isDefault: props.isDefault,
      feeSettings,
    });

    return this.db.$transaction(async () => {
      await this.feeSettingsRepository.create(feeSettings);

      return feeSettings;
    });
  }
}
