import { Test, TestingModule } from '@nestjs/testing';
import { RabbitMQClient } from '@sw-web/nestjs-core/rabbitmq';

import { RABBIT_MQ_EXCHANGES } from '@/core/constants';
import { DatabaseService } from '@/database';
import { Account } from '@/domain/account/account';
import {
  ACCOUNT_REPOSITORY_TOKEN,
  GATEWAY_REPOSITORY_TOKEN,
  PAYMENT_INTENT_REPOSITORY_TOKEN,
  PAYMENT_REPOSITORY_TOKEN,
  PAYMENT_SPLIT_REPOSITORY_TOKEN,
} from '@/domain/di-tokens';
import { Gateway } from '@/domain/gateway/gateway';
import { PaymentSettledIntegrationEvent } from '@/domain/payment/integration-events';
import { Payment } from '@/domain/payment/payment';
import { PaymentIntent } from '@/domain/payment-intent/payment-intent';
import { PaymentSplit } from '@/domain/payment-split/payment-split';
import { Gateway as SharedGateway, GatewayType, Money, PaymentMethodType } from '@/domain/shared';
import { RabbitMQClientMock } from '@/test/mocks/libs/rabbitmq-client.mock';
import { AccountRepositoryMock } from '@/test/mocks/repositories/account-repository.mock';
import { GatewayRepositoryMock } from '@/test/mocks/repositories/gateway-repository.mock';
import { PaymentIntentRepositoryMock } from '@/test/mocks/repositories/payment-intent-repository.mock';
import { PaymentRepositoryMock } from '@/test/mocks/repositories/payment-repository.mock';
import { PaymentSplitRepositoryMock } from '@/test/mocks/repositories/payment-split-repository.mock';
import { DatabaseServiceMock } from '@/test/mocks/services/database-service.mock';

import { CreateSettledPaymentCommand, CreateSettledPaymentCommandHandler } from '.';

describe('CreateSettledPaymentCommandHandler', () => {
  let handler: CreateSettledPaymentCommandHandler;
  let db: DatabaseServiceMock;
  let rabbitClient: RabbitMQClient;
  let gatewayRepository: GatewayRepositoryMock;
  let paymentRepository: PaymentRepositoryMock;
  let paymentSplitRepository: PaymentSplitRepositoryMock;
  let paymentIntentRepository: PaymentIntentRepositoryMock;
  let accountRepository: AccountRepositoryMock;
  let commandProps: CreateSettledPaymentCommand['props'];
  let gateway: Gateway;
  let payment: Payment;
  let paymentIntent: PaymentIntent;
  let paymentCreateSpy: jest.SpyInstance;
  let paymentSplitCreateSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateSettledPaymentCommandHandler,
        { provide: DatabaseService, useClass: DatabaseServiceMock },
        { provide: RabbitMQClient, useClass: RabbitMQClientMock },
        { provide: GATEWAY_REPOSITORY_TOKEN, useClass: GatewayRepositoryMock },
        { provide: PAYMENT_REPOSITORY_TOKEN, useClass: PaymentRepositoryMock },
        { provide: PAYMENT_SPLIT_REPOSITORY_TOKEN, useClass: PaymentSplitRepositoryMock },
        { provide: PAYMENT_INTENT_REPOSITORY_TOKEN, useClass: PaymentIntentRepositoryMock },
        { provide: ACCOUNT_REPOSITORY_TOKEN, useClass: AccountRepositoryMock },
      ],
    }).compile();

    handler = module.get<CreateSettledPaymentCommandHandler>(CreateSettledPaymentCommandHandler);
    db = module.get<DatabaseServiceMock>(DatabaseService);
    rabbitClient = module.get<RabbitMQClient>(RabbitMQClient);
    gatewayRepository = module.get<GatewayRepositoryMock>(GATEWAY_REPOSITORY_TOKEN);
    paymentRepository = module.get<PaymentRepositoryMock>(PAYMENT_REPOSITORY_TOKEN);
    paymentSplitRepository = module.get<PaymentSplitRepositoryMock>(PAYMENT_SPLIT_REPOSITORY_TOKEN);
    paymentIntentRepository = module.get<PaymentIntentRepositoryMock>(
      PAYMENT_INTENT_REPOSITORY_TOKEN,
    );
    accountRepository = module.get<AccountRepositoryMock>(ACCOUNT_REPOSITORY_TOKEN);
    [gateway] = gatewayRepository.db;
    [payment] = paymentRepository.db;
    [paymentIntent] = paymentIntentRepository.db;
    commandProps = {
      amount: 10,
      currency: 'USD',
      customerId: 'customer-id',
      gatewayType: gateway.getProps().type,
      marketplaceId: 'marketplace-id',
      paymentMethodId: 'payment-method-id',
      paymentIntentId: paymentIntent.getId(),
      paymentMethodType: PaymentMethodType.CARD,
      paymentSplits: accountRepository.db.map((account) => ({
        accountId: account.getId(),
        amount: 10,
      })),
      providerFee: 0,
    };
    paymentCreateSpy = jest.spyOn(Payment, 'create').mockReturnValue(payment);
    paymentSplitCreateSpy = jest.spyOn(PaymentSplit, 'create');
    paymentSplitRepository.db.forEach((split) => paymentSplitCreateSpy.mockReturnValueOnce(split));
    paymentSplitRepository.create = jest.fn();
    payment.markSettled = jest.fn();
    paymentRepository.create = jest.fn();
    rabbitClient.publish = jest.fn();

    db.catchTransactions = false;
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should fail if gateway not found', async () => {
    const command = new CreateSettledPaymentCommand({
      ...commandProps,
      gatewayType: 'invalid-gateway' as GatewayType,
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('invalid gateway'));
  });

  it('should fail if payment intent not found', async () => {
    const command = new CreateSettledPaymentCommand({
      ...commandProps,
      paymentIntentId: 'invalid-payment-intent-id',
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Invalid payment intent'));
  });

  it('should fail if any of the payment split accounts cannot be found', async () => {
    const command = new CreateSettledPaymentCommand({
      ...commandProps,
      paymentSplits: [{ accountId: 'invalid-account-id', amount: 10 }],
    });

    await expect(handler.execute(command)).rejects.toThrow(new Error('Account not found'));
  });

  it('should fail if any of the gateway accounts cannot be found', async () => {
    const command = new CreateSettledPaymentCommand(commandProps);
    const [account] = accountRepository.db;
    accountRepository.findById = jest.fn().mockResolvedValue(
      new Account({
        id: account.getId(),
        props: {
          ...account.getProps(),
          gatewayAccounts: [],
        },
      }),
    );

    await expect(handler.execute(command)).rejects.toThrow(new Error('Gateway account not found'));
  });

  it('should create a settled payment', async () => {
    const command = new CreateSettledPaymentCommand(commandProps);

    const result = await handler.execute(command);

    const paymentIntentProps = paymentIntent.getProps();
    expect(paymentCreateSpy).toHaveBeenCalledWith({
      paymentIntentId: commandProps.paymentIntentId,
      paymentMethodType: commandProps.paymentMethodType,
      marketplaceId: commandProps.marketplaceId,
      providerFee: paymentIntentProps.providerFee,
      amount: Money.from(commandProps.amount, commandProps.currency),
      customerId: commandProps.customerId,
      feeSettingsId: paymentIntentProps.feeSettingsId,
      gateway: new SharedGateway(gateway.getId(), gateway.getProps().type),
      platformPaymentFee: paymentIntentProps.platformPaymentFee,
      paymentMethodId: 'payment-method-id',
      platformPaymentFeeFixed: paymentIntentProps.platformPaymentFeeFixed,
      platformPaymentFeePercentage: paymentIntentProps.platformPaymentFeePercentage,
      marketplacePaymentFee: paymentIntentProps.marketplacePaymentFee,
      marketplacePaymentFeeFixed: paymentIntentProps.marketplacePaymentFeeFixed,
      marketplacePaymentFeePercentage: paymentIntentProps.marketplacePaymentFeePercentage,
      providerFeeFixed: paymentIntentProps.providerFeeFixed,
      providerFeePercentage: paymentIntentProps.providerFeePercentage,
      platformOrderFee: paymentIntentProps.platformOrderFee,
      marketplaceOrderFee: paymentIntentProps.marketplaceOrderFee,
    });

    expect(paymentRepository.create).toHaveBeenCalledWith(payment);
    // Ensure the payment is marked as settled before doing repository operations
    const [markSettledInvOrder] = (payment.markSettled as jest.Mock).mock.invocationCallOrder;
    const [createInvOrder] = (paymentRepository.create as jest.Mock).mock.invocationCallOrder;
    expect(markSettledInvOrder).toBeLessThan(createInvOrder);
    expect(result).toBe(payment);
  });

  it('should create payment splits', async () => {
    const command = new CreateSettledPaymentCommand(commandProps);

    await handler.execute(command);

    commandProps.paymentSplits.forEach((split, i) => {
      expect(paymentSplitCreateSpy).toHaveBeenCalledWith({
        accountId: split.accountId,
        amount: Money.from(split.amount, commandProps.currency),
        paymentId: payment.getId(),
        gateway: new SharedGateway(gateway.getId(), gateway.getProps().type),
      });
      const paymentSplit = paymentSplitCreateSpy.mock.results[i].value;
      expect(paymentSplitRepository.create).toHaveBeenCalledWith(paymentSplit);
    });
  });

  it('should publish payment settled integration event after successful creation', async () => {
    const command = new CreateSettledPaymentCommand(commandProps);
    // Mock the Date object to avoid time-based issues
    const fixedDate = new Date();
    jest.spyOn(global, 'Date').mockImplementation(() => fixedDate);
    const transactionSpy = jest.spyOn(db, '$transaction');

    await handler.execute(command);

    expect(transactionSpy).toHaveBeenCalled();
    expect(rabbitClient.publish).toHaveBeenCalledWith(
      RABBIT_MQ_EXCHANGES.payment.name,
      RABBIT_MQ_EXCHANGES.payment.routingKeys.paymentSettled.name,
      new PaymentSettledIntegrationEvent({
        aggregateId: payment.getId(),
        paymentIntentId: commandProps.paymentIntentId,
      }),
    );
    const [transactionInvOrder] = transactionSpy.mock.invocationCallOrder;
    const [publishInvOrder] = (rabbitClient.publish as jest.Mock).mock.invocationCallOrder;
    expect(publishInvOrder).toBeGreaterThan(transactionInvOrder);
  });

  it('should operate within a transaction', async () => {
    const command = new CreateSettledPaymentCommand(commandProps);
    const findAccountSpy = jest.spyOn(accountRepository, 'findById');
    db.catchTransactions = true;

    await handler.execute(command);

    expect(paymentRepository.create).not.toHaveBeenCalled();
    expect(findAccountSpy).not.toHaveBeenCalled();
    expect(paymentSplitCreateSpy).not.toHaveBeenCalled();
    expect(paymentSplitRepository.create).not.toHaveBeenCalled();
    expect(db.transactionCount).toBe(1);

    await db.invokeNextTransaction();

    const numOfPaymentSplits = commandProps.paymentSplits.length;
    expect(paymentRepository.create).toHaveBeenCalledWith(payment);
    expect(findAccountSpy).toHaveBeenCalledTimes(numOfPaymentSplits);
    expect(paymentSplitCreateSpy).toHaveBeenCalledTimes(numOfPaymentSplits);
    expect(paymentSplitRepository.create).toHaveBeenCalledTimes(numOfPaymentSplits);
  });
});
