import { Type } from 'class-transformer';
import {
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsPositive,
  IsString,
  ValidateNested,
} from 'class-validator';

import {
  GatewayType,
  AllowedCurrencies,
  PaymentMethodType,
  GatewayMetadata,
} from '@/domain/shared';

class PaymentSplitDataDto {
  @IsPositive()
  amount: number;

  @IsString()
  @IsNotEmpty()
  accountId: string;
}

export class CreatePaymentIntentRequestDto {
  @IsOptional()
  @IsString()
  businessEntityId: string | null;

  @IsPositive()
  amount: number;

  @IsNumber()
  marketplaceOrderFee: number;

  @IsNumber()
  platformOrderFee: number;

  @IsEnum(GatewayType)
  gatewayType: GatewayType;

  @IsString()
  @IsNotEmpty()
  customerId: string;

  @IsString()
  @IsOptional()
  feeSettingsId: string | null;

  @IsEnum(AllowedCurrencies)
  currency: AllowedCurrencies;

  @IsEnum(PaymentMethodType)
  paymentMethodType: PaymentMethodType;

  @ValidateNested({ each: true })
  @IsNotEmpty()
  @Type(() => PaymentSplitDataDto)
  paymentSplits: PaymentSplitDataDto[];

  @IsString()
  @IsOptional()
  statementDescriptor?: string | null;

  @IsObject()
  @IsOptional()
  metadata?: GatewayMetadata | null;
}
