import { FeeSettings } from '@/domain/fee-settings/fee-settings';

export class FeeSettingsResponseDto {
  id: string;
  marketplaceFeePercentage: number;
  marketplaceFeeFixed: number;
  isDefault: boolean;

  constructor(feeSettings: FeeSettings) {
    const props = feeSettings.getProps();
    this.id = props.id;
    this.marketplaceFeePercentage = props.marketplaceFeePercentage;
    this.marketplaceFeeFixed = props.marketplaceFeeFixed.getProps().amount.toNumber();
    this.isDefault = props.isDefault;
  }
}
