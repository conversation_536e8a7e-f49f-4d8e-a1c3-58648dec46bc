import { Body, Controller, Param, Post, UseGuards } from '@nestjs/common';
import { CommandBus } from '@nestjs/cqrs';

import { CreateFeeSettingsCommand } from '@/application/fee-settings/commands/create-fee-settings';
import { UpdateFeeSettingsCommand } from '@/application/fee-settings/commands/update-fee-settings';
import { FeeSettings } from '@/domain/fee-settings/fee-settings';
import { GatewayType } from '@/domain/shared';
import {
  MarketplaceApiKeyGuard,
  ReqMarketplace,
  RequestMarketplace,
} from '@/infrastructure/marketplace';

import { CreateFeeSettingsRequestDto, FeeSettingsResponseDto } from './dto';
import { UpdateFeeSettingsRequestDto } from './dto/update-fee-settings.request.dto';

@Controller()
export class FeeSettingsController {
  constructor(private readonly commandBus: CommandBus) {}

  @UseGuards(MarketplaceApiKeyGuard)
  @Post('marketplace-gateways/:gateway/fee-settings')
  async create(
    @Body() dto: CreateFeeSettingsRequestDto,
    @Param('gateway') gateway: GatewayType,
    @ReqMarketplace() marketplace: RequestMarketplace,
  ): Promise<FeeSettingsResponseDto> {
    const feeSettings = await this.commandBus.execute<CreateFeeSettingsCommand, FeeSettings>(
      new CreateFeeSettingsCommand({
        marketplaceFeePercentage: dto.marketplaceFeePercentage,
        marketplaceFeeFixed: dto.marketplaceFeeFixed,
        isDefault: dto.isDefault,
        gatewayType: gateway,
        marketplaceId: marketplace.id,
      }),
    );

    return new FeeSettingsResponseDto(feeSettings);
  }

  @UseGuards(MarketplaceApiKeyGuard)
  @Post('marketplace-gateways/:gateway/fee-settings/:feeSettingsId')
  async update(
    @Body() dto: UpdateFeeSettingsRequestDto,
    @Param('feeSettingsId') feeSettingsId: string,
    @Param('gateway') gateway: GatewayType,
  ): Promise<FeeSettingsResponseDto> {
    const feeSettings = await this.commandBus.execute<UpdateFeeSettingsCommand, FeeSettings>(
      new UpdateFeeSettingsCommand({
        marketplaceFeePercentage: dto.marketplaceFeePercentage,
        marketplaceFeeFixed: dto.marketplaceFeeFixed,
        isDefault: dto.isDefault,
        gatewayType: gateway,
        feeSettingsId,
      }),
    );

    return new FeeSettingsResponseDto(feeSettings);
  }
}
