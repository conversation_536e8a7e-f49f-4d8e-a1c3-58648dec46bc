import { AggregateRoot, idFactory } from '@/core/ddd';

import { FeeSettingsCreatedDomainEvent, FeeSettingsUpdatedDomainEvent } from './events';
import { Money } from '../shared';

export type EntityProps = {
  platformFeeFixed: Money;
  platformFeePercentage: number;
  marketplaceFeeFixed: Money;
  marketplaceFeePercentage: number;
  marketplaceGatewayId: string;
  isDefault: boolean;
};

export class FeeSettings extends AggregateRoot<EntityProps> {
  static create(props: EntityProps) {
    const feeSettings = new FeeSettings({
      id: idFactory(),
      props,
    });

    feeSettings.addEvent(new FeeSettingsCreatedDomainEvent({ aggregateId: feeSettings.getId() }));

    return feeSettings;
  }

  update(props: Partial<EntityProps>) {
    this.props = {
      ...this.props,
      ...props,
    };

    this.addEvent(new FeeSettingsUpdatedDomainEvent({ aggregateId: this.getId() }));
  }
}
